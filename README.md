# Photon Email Updater - Azure Function

This Azure Function application processes email update requests from a Service Bus queue.

## Project Structure

- `EmailUpdateFunction.cs` - Main Azure Function with Service Bus queue trigger
- `PhotonEmailUpdater.csproj` - Project file with dependencies
- `Program.cs` - Application entry point and configuration
- `local.settings.json` - Local development settings
- `host.json` - Azure Functions host configuration

## Prerequisites

1. **.NET 8.0 SDK** - Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
2. **Azure Functions Core Tools** - Install using:
   ```bash
   npm install -g azure-functions-core-tools@4 --unsafe-perm true
   ```
3. **Azure Service Bus** - Either:
   - Azure Service Bus namespace in Azure
   - Azure Service Bus Emulator for local development

## Configuration

### Local Development

1. Update `local.settings.json` with your Service Bus connection string:
   ```json
   {
       "IsEncrypted": false,
       "Values": {
           "AzureWebJobsStorage": "UseDevelopmentStorage=true",
           "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
           "ServiceBusConnection": "Endpoint=sb://your-servicebus-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-shared-access-key"
       }
   }
   ```

2. Create a Service Bus queue named `email-update-queue` in your Service Bus namespace.

### Azure Deployment

For Azure deployment, configure the following application settings:
- `ServiceBusConnection` - Your Service Bus connection string

## Message Format

The function expects JSON messages in the following format:

```json
{
    "UserId": "user123",
    "OldEmail": "<EMAIL>",
    "NewEmail": "<EMAIL>",
    "RequestedAt": "2024-01-01T12:00:00Z",
    "RequestedBy": "admin"
}
```

## Building and Running

### Build the Project
```bash
dotnet build PhotonEmailUpdater.csproj
```

### Run Locally
```bash
func start
```

Or using dotnet:
```bash
dotnet run
```

### Test the Function

You can test the function by sending a message to the Service Bus queue. Here's an example using Azure CLI:

```bash
az servicebus message send \
    --resource-group your-resource-group \
    --namespace-name your-servicebus-namespace \
    --queue-name email-update-queue \
    --body '{"UserId":"test123","OldEmail":"<EMAIL>","NewEmail":"<EMAIL>","RequestedAt":"2024-01-01T12:00:00Z","RequestedBy":"admin"}'
```

## Function Features

- **Service Bus Queue Trigger**: Automatically processes messages from the `email-update-queue`
- **JSON Deserialization**: Parses incoming messages into strongly-typed objects
- **Error Handling**: Proper exception handling with logging
- **Dead Letter Queue**: Failed messages are moved to dead letter queue for investigation
- **Structured Logging**: Comprehensive logging for monitoring and debugging

## Monitoring

The function includes Application Insights integration for monitoring:
- Function execution metrics
- Error tracking
- Performance monitoring
- Custom telemetry

## Next Steps

1. Implement the actual email update logic in `ProcessEmailUpdate` method
2. Add database connectivity for user management
3. Implement email validation
4. Add unit tests
5. Set up CI/CD pipeline for deployment

## Dependencies

- Microsoft.Azure.Functions.Worker (2.0.0)
- Microsoft.Azure.Functions.Worker.Extensions.ServiceBus (5.23.0)
- Microsoft.Azure.Functions.Worker.ApplicationInsights (2.0.0)
- Microsoft.ApplicationInsights.WorkerService (2.23.0)
