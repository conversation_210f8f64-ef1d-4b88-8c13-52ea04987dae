# Photon Email Updater - Azure Function

This Azure Function application processes email update requests. Currently implemented with HTTP trigger, with Service Bus queue trigger ready to be added.

## Project Structure

- `EmailUpdateFunction.cs` - Main Azure Function with HTTP trigger for email updates
- `PhotonEmailUpdater.csproj` - Project file with dependencies
- `Program.cs` - Application entry point and configuration
- `local.settings.json` - Local development settings
- `host.json` - Azure Functions host configuration
- `README.md` - This documentation file

## Prerequisites

1. **.NET 8.0 SDK** - Download from [Microsoft .NET](https://dotnet.microsoft.com/download)
2. **Azure Functions Core Tools** - Install using:
   ```bash
   npm install -g azure-functions-core-tools@4 --unsafe-perm true
   ```

## Current Status

✅ **Working**: HTTP trigger function for email updates
🔄 **In Progress**: Service Bus queue trigger (requires dependency resolution)

## Configuration

### Local Development

The function is currently configured to run with HTTP triggers. No additional configuration is needed for basic operation.

For future Service Bus integration, you would need to:
1. Update `local.settings.json` with your Service Bus connection string
2. Create a Service Bus queue named `email-update-queue`

### Azure Deployment

For Azure deployment, the function can be deployed as-is with HTTP triggers.

## Message Format

The function expects JSON messages in the following format:

```json
{
    "UserId": "user123",
    "OldEmail": "<EMAIL>",
    "NewEmail": "<EMAIL>",
    "RequestedAt": "2024-01-01T12:00:00Z",
    "RequestedBy": "admin"
}
```

## Building and Running

### Build the Project
```bash
dotnet build PhotonEmailUpdater.csproj
```

### Run Locally
```bash
dotnet run --project PhotonEmailUpdater.csproj -- --port 7190
```

The function will start and be available at: `http://localhost:7190/api/EmailUpdateFunction`

### Test the Function

You can test the HTTP trigger function using curl:

```bash
curl -X POST http://localhost:7190/api/EmailUpdateFunction \
  -H "Content-Type: application/json" \
  -d '{
    "UserId": "test123",
    "OldEmail": "<EMAIL>",
    "NewEmail": "<EMAIL>",
    "RequestedAt": "2024-01-01T12:00:00Z",
    "RequestedBy": "admin"
  }'
```

Expected response:
```
Email update processed successfully for user test123
```

## Function Features

- **HTTP Trigger**: Processes email update requests via HTTP POST
- **JSON Deserialization**: Parses incoming requests into strongly-typed objects
- **Error Handling**: Proper exception handling with appropriate HTTP status codes
- **Input Validation**: Validates request format and required fields
- **Structured Logging**: Comprehensive logging for monitoring and debugging
- **Async Processing**: Simulates email update processing with proper async/await patterns

## Monitoring

The function includes Application Insights integration for monitoring:
- Function execution metrics
- Error tracking
- Performance monitoring
- Custom telemetry

## Adding Service Bus Queue Trigger

To add Service Bus queue trigger functionality:

1. **Install Service Bus Extension**:
   ```bash
   dotnet add package Microsoft.Azure.Functions.Worker.Extensions.ServiceBus
   ```

2. **Create Service Bus Trigger Function**:
   ```csharp
   [Function("ServiceBusEmailUpdateFunction")]
   public async Task Run(
       [ServiceBusTrigger("email-update-queue", Connection = "ServiceBusConnection")]
       string message)
   {
       // Process Service Bus message
   }
   ```

3. **Update local.settings.json**:
   ```json
   {
       "Values": {
           "ServiceBusConnection": "your-connection-string"
       }
   }
   ```

Note: There are currently dependency conflicts with the Service Bus extension that need to be resolved.

## Next Steps

1. Resolve Service Bus extension dependency conflicts
2. Implement the actual email update logic in `ProcessEmailUpdate` method
3. Add database connectivity for user management
4. Implement email validation
5. Add unit tests
6. Set up CI/CD pipeline for deployment

## Dependencies

- Microsoft.Azure.Functions.Worker (2.0.0)
- Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore (2.0.1)
- Microsoft.Azure.Functions.Worker.ApplicationInsights (2.0.0)
- Microsoft.ApplicationInsights.WorkerService (2.23.0)
- Microsoft.Extensions.Configuration.Abstractions (8.0.0)
