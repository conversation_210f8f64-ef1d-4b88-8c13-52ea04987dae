using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace PhotonEmailUpdater
{
    public class EmailUpdateFunction
    {
        private readonly ILogger<EmailUpdateFunction> _logger;

        public EmailUpdateFunction(ILogger<EmailUpdateFunction> logger)
        {
            _logger = logger;
        }

        [Function("EmailUpdateFunction")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequestData req)
        {
            _logger.LogInformation("HTTP trigger function processed a request.");

            try
            {
                // Read the request body
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

                if (string.IsNullOrEmpty(requestBody))
                {
                    _logger.LogError("Request body is empty");
                    var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await badResponse.WriteStringAsync("Request body cannot be empty");
                    return badResponse;
                }

                // Parse the message
                var emailUpdateRequest = JsonSerializer.Deserialize<EmailUpdateRequest>(requestBody);

                if (emailUpdateRequest == null)
                {
                    _logger.LogError("Failed to deserialize request body: {requestBody}", requestBody);
                    var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await badResponse.WriteStringAsync("Invalid request format");
                    return badResponse;
                }

                _logger.LogInformation("Processing email update for User ID: {userId}, New Email: {newEmail}",
                    emailUpdateRequest.UserId, emailUpdateRequest.NewEmail);

                // Process the email update
                await ProcessEmailUpdate(emailUpdateRequest);

                _logger.LogInformation("Successfully processed email update for User ID: {userId}",
                    emailUpdateRequest.UserId);

                var response = req.CreateResponse(HttpStatusCode.OK);
                await response.WriteStringAsync($"Email update processed successfully for user {emailUpdateRequest.UserId}");
                return response;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing request body");
                var errorResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                await errorResponse.WriteStringAsync("Invalid JSON format");
                return errorResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing email update request");
                var errorResponse = req.CreateResponse(HttpStatusCode.InternalServerError);
                await errorResponse.WriteStringAsync("Internal server error");
                return errorResponse;
            }
        }

        private async Task ProcessEmailUpdate(EmailUpdateRequest request)
        {
            // Simulate email update processing
            _logger.LogInformation("Updating email for user {userId} from {oldEmail} to {newEmail}", 
                request.UserId, request.OldEmail, request.NewEmail);

            // Add your business logic here:
            // 1. Validate the new email address
            // 2. Update the user's email in the database
            // 3. Send confirmation email
            // 4. Update any related systems
            
            await Task.Delay(100); // Simulate processing time
            
            _logger.LogInformation("Email update completed for user {userId}", request.UserId);
        }
    }

    public class EmailUpdateRequest
    {
        public string UserId { get; set; } = string.Empty;
        public string OldEmail { get; set; } = string.Empty;
        public string NewEmail { get; set; } = string.Empty;
        public DateTime RequestedAt { get; set; }
        public string RequestedBy { get; set; } = string.Empty;
    }
}
