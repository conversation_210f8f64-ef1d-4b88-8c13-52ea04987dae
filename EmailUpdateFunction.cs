using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace PhotonEmailUpdater
{
    public class EmailUpdateFunction
    {
        private readonly ILogger<EmailUpdateFunction> _logger;

        public EmailUpdateFunction(ILogger<EmailUpdateFunction> logger)
        {
            _logger = logger;
        }

        [Function("EmailUpdateFunction")]
        public async Task Run(
            [ServiceBusTrigger("email-update-queue", Connection = "ServiceBusConnection")] 
            string message)
        {
            _logger.LogInformation("Service Bus queue trigger function processed message: {message}", message);

            try
            {
                // Parse the message
                var emailUpdateRequest = JsonSerializer.Deserialize<EmailUpdateRequest>(message);
                
                if (emailUpdateRequest == null)
                {
                    _logger.LogError("Failed to deserialize message: {message}", message);
                    return;
                }

                _logger.LogInformation("Processing email update for User ID: {userId}, New Email: {newEmail}", 
                    emailUpdateRequest.UserId, emailUpdateRequest.NewEmail);

                // TODO: Add your email update logic here
                await ProcessEmailUpdate(emailUpdateRequest);

                _logger.LogInformation("Successfully processed email update for User ID: {userId}", 
                    emailUpdateRequest.UserId);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing message: {message}", message);
                throw; // Re-throw to move message to dead letter queue
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing email update message: {message}", message);
                throw; // Re-throw to move message to dead letter queue
            }
        }

        private async Task ProcessEmailUpdate(EmailUpdateRequest request)
        {
            // Simulate email update processing
            _logger.LogInformation("Updating email for user {userId} from {oldEmail} to {newEmail}", 
                request.UserId, request.OldEmail, request.NewEmail);

            // Add your business logic here:
            // 1. Validate the new email address
            // 2. Update the user's email in the database
            // 3. Send confirmation email
            // 4. Update any related systems
            
            await Task.Delay(100); // Simulate processing time
            
            _logger.LogInformation("Email update completed for user {userId}", request.UserId);
        }
    }

    public class EmailUpdateRequest
    {
        public string UserId { get; set; } = string.Empty;
        public string OldEmail { get; set; } = string.Empty;
        public string NewEmail { get; set; } = string.Empty;
        public DateTime RequestedAt { get; set; }
        public string RequestedBy { get; set; } = string.Empty;
    }
}
