[{"name": "EmailUpdateFunction", "scriptFile": "PhotonEmailUpdater.dll", "entryPoint": "PhotonEmailUpdater.EmailUpdateFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "message", "direction": "In", "type": "serviceBusTrigger", "dataType": "String", "queueName": "email-update-queue", "connection": "ServiceBusConnection", "cardinality": "One", "properties": {}}]}]