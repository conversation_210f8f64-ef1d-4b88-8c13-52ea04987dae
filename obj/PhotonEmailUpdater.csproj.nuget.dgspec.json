{"format": 1, "restore": {"/Users/<USER>/Projects/SGS/POC/photon-email-updater/PhotonEmailUpdater.csproj": {}}, "projects": {"/Users/<USER>/Projects/SGS/POC/photon-email-updater/PhotonEmailUpdater.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/PhotonEmailUpdater.csproj", "projectName": "PhotonEmailUpdater", "projectPath": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/PhotonEmailUpdater.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/Users/<USER>/Projects/SGS/Code/Adobe-DataLogic": {}, "/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.ApplicationInsights.WorkerService": {"target": "Package", "version": "[2.23.0, )"}, "Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.Azure.Functions.Worker.ApplicationInsights": {"target": "Package", "version": "[2.0.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore": {"target": "Package", "version": "[2.0.1, )"}, "Microsoft.Azure.Functions.Worker.Extensions.ServiceBus": {"target": "Package", "version": "[5.23.0, )"}, "Microsoft.Azure.Functions.Worker.Sdk": {"target": "Package", "version": "[2.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Host.osx-arm64", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}