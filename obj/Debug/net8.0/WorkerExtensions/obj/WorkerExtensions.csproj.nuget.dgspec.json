{"format": 1, "restore": {"/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/Debug/net8.0/WorkerExtensions/WorkerExtensions.csproj": {}}, "projects": {"/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/Debug/net8.0/WorkerExtensions/WorkerExtensions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/Debug/net8.0/WorkerExtensions/WorkerExtensions.csproj", "projectName": "Microsoft.Azure.Functions.Worker.Extensions", "projectPath": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/Debug/net8.0/WorkerExtensions/WorkerExtensions.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/Debug/net8.0/WorkerExtensions/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/Users/<USER>/Projects/SGS/Code/Adobe-DataLogic": {}, "/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Azure.WebJobs.Extensions.ServiceBus": {"target": "Package", "version": "[5.17.0, )"}, "Microsoft.NET.Sdk.Functions": {"target": "Package", "version": "[4.6.0, )"}, "Microsoft.NETCore.Targets": {"suppressParent": "All", "target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.15, 8.0.15]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.15, 8.0.15]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}