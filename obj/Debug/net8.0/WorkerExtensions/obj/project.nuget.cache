{"version": 2, "dgSpecHash": "7LfZ3tiQlzo=", "success": true, "projectFilePath": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/obj/Debug/net8.0/WorkerExtensions/WorkerExtensions.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/azure.core/1.46.2/azure.core.1.46.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.core.amqp/1.3.1/azure.core.amqp.1.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.identity/1.13.1/azure.identity.1.13.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.messaging.servicebus/7.20.1/azure.messaging.servicebus.7.20.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/google.protobuf/3.24.3/google.protobuf.3.24.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.aspnetcore/2.49.0/grpc.aspnetcore.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.aspnetcore.server/2.49.0/grpc.aspnetcore.server.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.aspnetcore.server.clientfactory/2.49.0/grpc.aspnetcore.server.clientfactory.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.core.api/2.49.0/grpc.core.api.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.net.client/2.49.0/grpc.net.client.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.net.clientfactory/2.49.0/grpc.net.clientfactory.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.net.common/2.49.0/grpc.net.common.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.tools/2.49.0/grpc.tools.2.49.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnet.webapi.client/5.2.8/microsoft.aspnet.webapi.client.5.2.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.abstractions/2.2.0/microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.core/2.2.0/microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authorization/2.2.0/microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authorization.policy/2.2.0/microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.hosting.abstractions/2.2.0/microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.hosting.server.abstractions/2.2.0/microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http/2.2.2/microsoft.aspnetcore.http.2.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.abstractions/2.2.0/microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.extensions/2.2.0/microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.features/2.2.0/microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.jsonpatch/2.2.0/microsoft.aspnetcore.jsonpatch.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.abstractions/2.2.0/microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.core/2.2.0/microsoft.aspnetcore.mvc.core.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.formatters.json/2.2.0/microsoft.aspnetcore.mvc.formatters.json.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.webapicompatshim/2.2.0/microsoft.aspnetcore.mvc.webapicompatshim.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.responsecaching.abstractions/2.2.0/microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.routing/2.2.2/microsoft.aspnetcore.routing.2.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.routing.abstractions/2.2.0/microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.webutilities/2.2.0/microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.amqp/2.7.0/microsoft.azure.amqp.2.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.analyzers/1.0.0/microsoft.azure.functions.analyzers.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs/3.0.37/microsoft.azure.webjobs.3.0.37.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.core/3.0.37/microsoft.azure.webjobs.core.3.0.37.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.extensions/3.0.6/microsoft.azure.webjobs.extensions.3.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.extensions.http/3.2.0/microsoft.azure.webjobs.extensions.http.3.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.extensions.rpc/3.0.37/microsoft.azure.webjobs.extensions.rpc.3.0.37.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.extensions.servicebus/5.17.0/microsoft.azure.webjobs.extensions.servicebus.5.17.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.host.storage/3.0.14/microsoft.azure.webjobs.host.storage.3.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.rpc.core/3.0.37/microsoft.azure.webjobs.rpc.core.3.0.37.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.script.extensionsmetadatagenerator/4.0.1/microsoft.azure.webjobs.script.extensionsmetadatagenerator.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/8.0.0/microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.csharp/4.5.0/microsoft.csharp.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.dotnet.platformabstractions/2.1.0/microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.azure/1.12.0/microsoft.extensions.azure.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/8.0.0/microsoft.extensions.configuration.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/8.0.0/microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/8.0.2/microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.environmentvariables/2.1.0/microsoft.extensions.configuration.environmentvariables.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/2.1.0/microsoft.extensions.configuration.fileextensions.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/2.1.0/microsoft.extensions.configuration.json.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.1/microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.2/microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/2.1.0/microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/2.2.0/microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/2.1.0/microsoft.extensions.fileproviders.physical.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/2.1.0/microsoft.extensions.filesystemglobbing.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting/2.1.0/microsoft.extensions.hosting.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/2.2.0/microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.http/3.0.3/microsoft.extensions.http.3.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.1/microsoft.extensions.logging.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.3/microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.configuration/2.1.0/microsoft.extensions.logging.configuration.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.objectpool/2.2.0/microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.2/microsoft.extensions.options.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/2.1.0/microsoft.extensions.options.configurationextensions.2.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client/4.66.1/microsoft.identity.client.4.66.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client.extensions.msal/4.66.1/microsoft.identity.client.extensions.msal.4.66.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/6.35.0/microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.http.headers/2.2.0/microsoft.net.http.headers.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.sdk.functions/4.6.0/microsoft.net.sdk.functions.4.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.1/microsoft.netcore.platforms.1.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/3.0.0/microsoft.netcore.targets.3.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.primitives/4.3.0/microsoft.win32.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/ncrontab.signed/3.3.0/ncrontab.signed.3.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/netstandard.library/1.6.1/netstandard.library.1.6.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json.bson/1.0.1/newtonsoft.json.bson.1.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system/4.3.0/runtime.native.system.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.io.compression/4.3.0/runtime.native.system.io.compression.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.net.http/4.3.0/runtime.native.system.net.http.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography.apple/4.3.0/runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography.openssl/4.3.2/runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.appcontext/4.3.0/system.appcontext.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.5.0/system.buffers.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.clientmodel/1.4.2/system.clientmodel.1.4.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections/4.3.0/system.collections.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.concurrent/4.3.0/system.collections.concurrent.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/4.4.0/system.componentmodel.annotations.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.console/4.3.0/system.console.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.debug/4.3.0/system.diagnostics.debug.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/6.0.1/system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tools/4.3.0/system.diagnostics.tools.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracesource/4.3.0/system.diagnostics.tracesource.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracing/4.3.0/system.diagnostics.tracing.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.dynamic.runtime/4.0.11/system.dynamic.runtime.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization/4.3.0/system.globalization.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.calendars/4.3.0/system.globalization.calendars.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.extensions/4.3.0/system.globalization.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.compression/4.3.0/system.io.compression.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.compression.zipfile/4.3.0/system.io.compression.zipfile.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem/4.3.0/system.io.filesystem.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.primitives/4.3.0/system.io.filesystem.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq/4.3.0/system.linq.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq.expressions/4.3.0/system.linq.expressions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.5/system.memory.4.5.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory.data/6.0.1/system.memory.data.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.http/4.3.4/system.net.http.4.3.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.primitives/4.3.0/system.net.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.sockets/4.3.0/system.net.sockets.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.objectmodel/4.3.0/system.objectmodel.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.3.0/system.reflection.emit.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.ilgeneration/4.3.0/system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.lightweight/4.3.0/system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.extensions/4.3.0/system.reflection.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.typeextensions/4.3.0/system.reflection.typeextensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.resourcemanager/4.3.0/system.resources.resourcemanager.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.1/system.runtime.4.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.extensions/4.3.0/system.runtime.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.handles/4.3.0/system.runtime.handles.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices/4.3.0/system.runtime.interopservices.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices.runtimeinformation/4.3.0/system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.loader/4.3.0/system.runtime.loader.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.numerics/4.3.0/system.runtime.numerics.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.algorithms/4.3.0/system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/4.3.0/system.security.cryptography.cng.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.csp/4.3.0/system.security.cryptography.csp.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.encoding/4.3.0/system.security.cryptography.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.openssl/4.3.0/system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.primitives/4.3.0/system.security.cryptography.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.protecteddata/4.5.0/system.security.cryptography.protecteddata.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.x509certificates/4.3.0/system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.extensions/4.3.0/system.text.encoding.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/6.0.0/system.text.encodings.web.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/6.0.10/system.text.json.6.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.regularexpressions/4.3.1/system.text.regularexpressions.4.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading/4.3.0/system.threading.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.dataflow/4.8.0/system.threading.tasks.dataflow.4.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.timer/4.3.0/system.threading.timer.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.readerwriter/4.3.0/system.xml.readerwriter.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xdocument/4.3.0/system.xml.xdocument.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/windowsazure.storage/9.3.1/windowsazure.storage.9.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.15/microsoft.netcore.app.ref.8.0.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.15/microsoft.aspnetcore.app.ref.8.0.15.nupkg.sha512"], "logs": []}