{"version": 2, "dgSpecHash": "YtowQDMC/K4=", "success": true, "projectFilePath": "/Users/<USER>/Projects/SGS/POC/photon-email-updater/PhotonEmailUpdater.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/azure.core/1.44.1/azure.core.1.44.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.identity/1.12.0/azure.identity.1.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/google.protobuf/3.28.0/google.protobuf.3.28.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.core.api/2.65.0/grpc.core.api.2.65.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.net.client/2.65.0/grpc.net.client.2.65.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.net.clientfactory/2.65.0/grpc.net.clientfactory.2.65.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/grpc.net.common/2.65.0/grpc.net.common.2.65.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights/2.23.0/microsoft.applicationinsights.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights.dependencycollector/2.23.0/microsoft.applicationinsights.dependencycollector.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights.eventcountercollector/2.23.0/microsoft.applicationinsights.eventcountercollector.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights.perfcountercollector/2.23.0/microsoft.applicationinsights.perfcountercollector.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights.windowsserver/2.23.0/microsoft.applicationinsights.windowsserver.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights.windowsserver.telemetrychannel/2.23.0/microsoft.applicationinsights.windowsserver.telemetrychannel.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.applicationinsights.workerservice/2.23.0/microsoft.applicationinsights.workerservice.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker/2.0.0/microsoft.azure.functions.worker.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.applicationinsights/2.0.0/microsoft.azure.functions.worker.applicationinsights.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.core/2.0.0/microsoft.azure.functions.worker.core.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.extensions.abstractions/1.3.0/microsoft.azure.functions.worker.extensions.abstractions.1.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.extensions.http/3.2.0/microsoft.azure.functions.worker.extensions.http.3.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.extensions.http.aspnetcore/2.0.1/microsoft.azure.functions.worker.extensions.http.aspnetcore.2.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.extensions.http.aspnetcore.analyzers/1.0.3/microsoft.azure.functions.worker.extensions.http.aspnetcore.analyzers.1.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.grpc/2.0.0/microsoft.azure.functions.worker.grpc.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.sdk/2.0.2/microsoft.azure.functions.worker.sdk.2.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.sdk.analyzers/1.2.2/microsoft.azure.functions.worker.sdk.analyzers.1.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.functions.worker.sdk.generators/1.3.4/microsoft.azure.functions.worker.sdk.generators.1.3.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/8.0.0/microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/1.0.0/microsoft.extensions.caching.abstractions.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/1.0.0/microsoft.extensions.caching.memory.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/8.0.0/microsoft.extensions.configuration.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/8.0.0/microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/8.0.2/microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.commandline/8.0.0/microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.environmentvariables/8.0.0/microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/8.0.1/microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/8.0.1/microsoft.extensions.configuration.json.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.usersecrets/8.0.1/microsoft.extensions.configuration.usersecrets.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.1/microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.2/microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics/8.0.1/microsoft.extensions.diagnostics.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/8.0.1/microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/8.0.0/microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/8.0.0/microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/8.0.0/microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting/8.0.1/microsoft.extensions.hosting.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/8.0.1/microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.http/6.0.0/microsoft.extensions.http.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.1/microsoft.extensions.logging.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.2/microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.applicationinsights/2.23.0/microsoft.extensions.logging.applicationinsights.2.23.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.configuration/8.0.1/microsoft.extensions.logging.configuration.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.console/8.0.1/microsoft.extensions.logging.console.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.debug/8.0.1/microsoft.extensions.logging.debug.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.eventlog/8.0.1/microsoft.extensions.logging.eventlog.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.eventsource/8.0.1/microsoft.extensions.logging.eventsource.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.2/microsoft.extensions.options.8.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/8.0.0/microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client/4.61.3/microsoft.identity.client.4.61.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client.extensions.msal/4.61.3/microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/6.35.0/microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/1.0.1/microsoft.netcore.platforms.1.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.0.1/microsoft.netcore.targets.1.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/6.0.0/microsoft.win32.systemevents.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.clientmodel/1.1.0/system.clientmodel.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections/4.0.11/system.collections.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.configuration.configurationmanager/6.0.0/system.configuration.configurationmanager.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.debug/4.0.11/system.diagnostics.debug.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/6.0.1/system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.eventlog/8.0.1/system.diagnostics.eventlog.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.performancecounter/6.0.0/system.diagnostics.performancecounter.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.drawing.common/6.0.0/system.drawing.common.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization/4.0.11/system.globalization.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.1.0/system.io.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.accesscontrol/4.7.0/system.io.filesystem.accesscontrol.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq/4.1.0/system.linq.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory.data/6.0.0/system.memory.data.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.numerics.vectors/4.5.0/system.numerics.vectors.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.1.0/system.reflection.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.0.1/system.reflection.primitives.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.resourcemanager/4.0.1/system.resources.resourcemanager.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.1.0/system.runtime.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.extensions/4.1.0/system.runtime.extensions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/6.0.0/system.security.accesscontrol.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.protecteddata/6.0.0/system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.permissions/6.0.0/system.security.permissions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/4.7.0/system.security.principal.windows.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.0.11/system.text.encoding.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/6.0.0/system.text.encodings.web.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/6.0.10/system.text.json.6.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading/4.0.11/system.threading.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.0.11/system.threading.tasks.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.windows.extensions/6.0.0/system.windows.extensions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.15/microsoft.netcore.app.ref.8.0.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.15/microsoft.aspnetcore.app.ref.8.0.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.host.osx-arm64/8.0.15/microsoft.netcore.app.host.osx-arm64.8.0.15.nupkg.sha512"], "logs": []}